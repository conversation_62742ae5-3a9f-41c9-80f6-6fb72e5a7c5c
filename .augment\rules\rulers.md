---
type: "manual"
---

Simplified Chinese reply.
Output in markdown format.

#### **第一部分：平台规则与内容红线**

在动笔之前，必须严格遵守平台的规定，这是作品能否上架和签约的基础。

*   **严守原创性**：平台严禁任何形式的抄袭、洗稿或过度借鉴。AI生成的内容也需要经过您的严格把关和修改，确保其原创性。
*   **规避违规题材**：坚决不碰涉黄、涉政、涉军、涉黑、涉暴等敏感题材。对于暴力和犯罪情节的描写要适度，不能过度渲染。
*   **人设与情感线合规**：
    *   主角必须成年后才能发展感情线。
    *   主要角色之间不能存在真实的血缘或法律上的亲缘关系。
    *   现代背景下，男主角不能设定为军人，避免涉及军婚题材。
*   **杜绝封建迷信**：避免创作如冥婚、阴婚等不符合主流价值观的情节。

#### **第二部分：精准定位与选题策略**

明确的目标读者和题材是成功的一半。根据番茄小说平台的读者偏好调研，可以得出以下结论：

*   **读者年轻化趋势**：年轻读者偏爱节奏快、爽点密集的题材，如甜宠、系统、玄幻、重生等。
*   **热门题材**：
    *   **男频**：玄幻、都市修真、系统、奶爸、女婿、神医等。
    *   **女频**：现代言情（特别是总裁文）、古代言情（女强、团宠、嫡女）、甜宠、萌宝等。
*   **规则设定**：
    1.  **明确目标读者**：在创作初期就清晰定位你的小说是写给哪个年龄段、哪种性别的读者。
    2.  **选择热门或有潜力的题材**：新手作者建议从平台热门或自己擅长的题材入手，更容易获得流量。
    3.  **结合热点与创新**：可以在经典套路的基础上，结合当下的网络热点进行微创新，让故事更有新意。

#### **第三部分：黄金开头与剧情节奏**

网络小说的开头至关重要，通常被称为“黄金三章”，它直接决定了读者的去留。

*   **开门见山，快速入题**：第一章就应该直接进入核心场景，交代主角的身份、处境和面临的冲突。 避免冗长的背景设定和与主线无关的描写。
*   **制造冲突与悬念**：开头就要有明确的危机或目标，让读者迅速产生代入感和追读的欲望。
*   **规则设定**：
    1.  **前三章必须完成**：主角登场、金手指（或核心优势）亮相、核心矛盾出现、打脸情节（或爽点）的设置。
    2.  **快节奏推进**：减少不必要的铺垫，让剧情紧凑，爽点密集。番茄的读者更偏爱直接、不拖沓的叙事风格。
    3.  **稳定更新**：签约后保持稳定的日更（建议4000-6000字）是维持读者粘性的关键。

#### **第四部分：人设塑造与避免“毒点”**

立体且讨喜的人设是吸引读者的核心要素。

*   **主角设定**：
    *   **性格鲜明**：主角需要有明确的性格标签，无论是杀伐果断、还是幽默风趣，都要贯穿始终。
    *   **目标明确**：主角从始至终都要有清晰的行动目标，这是推动剧情发展的核心动力。
*   **配角设定**：配角的功能性要明确，服务于主角和主线剧情，避免出现抢戏或无意义的配角。
*   **常见“雷点”与“毒点”规避规则**：
    1.  **主角不圣母**：主角可以善良，但绝不能没有底线地原谅敌人，更不能因为妇人之仁而让自身和亲友陷入险境。
    2.  **不无脑送女**：避免主角身边出现无故对主角死心塌地，甚至牺牲自己成全他人的女性角色。
    3.  **不强行降智**：反派的行为逻辑要合理，不能为了凸显主角的强大而刻意降低反派的智商。
    4.  **金手指要合理**：金手指可以强大，但不能毫无限制，需要有成长和升级的过程。
    5.  **避免“文青式”说教**：网络小说以娱乐为主，避免大段的哲学思辨和人生感悟，用剧情和行动来体现人物思想。

#### **第五部分：世界观与设定的“防崩塌”规则**

网络小说篇幅长，一个稳定且有趣的世界观是留住读者的基石。

*   **规则5.1：设定前置与“设定集”原则**
    *   在动笔前，或是在AI的辅助下，创建一个独立的“设定集”文档。
    *   **内容包括**：力量体系（等级划分、晋升条件、各等级标志性能力）、地图（国家、势力分布、禁地区域）、货币与经济体系、特殊种族或血脉、历史背景（重大的历史事件、传说人物）。
    *   **AI指令示例**：“请为我的玄幻小说设计一个以‘灵根’为基础的修仙等级体系，要求分为九个大境界，每个境界的名称要有古风韵味，并简述其核心特征和突破难度。”

*   **规则5.2：“挤牙膏”式披露原则**
    *   不要在开篇就将所有设定抛给读者（信息过载）。
    *   将世界观设定融入剧情，通过主角的经历和对话，一点点揭示给读者。每一小段剧情只透露与当前情节最相关的设定。
    *   **AI指令示例**：“在主角第一次进入‘藏经阁’的剧情中，通过一位长老的口，向主角（和读者）介绍功法的‘天、地、玄、黄’四个等级，并让他选择一本黄级功法。”

#### **第六部分：升级体系与爽点密度的“心流”规则**

让读者沉浸其中，持续追读的关键在于节奏的把控。

*   **规则6.1：目标闭环与“地图”切换原则**
    *   将小说划分为一个个“地图”或“副本”（例如：新手村 -> 小镇 -> 主城 -> 宗门 -> 王国）。
    *   在每个地图中，为主角设定一个明确的、可达成的**短期总目标**（如：成为家族第一、赢得学院大比）。当这个目标完成后，立即引入下一个地图的线索，驱动主角前进。这会形成一个“解决问题-获得奖励-发现新问题”的循环，让读者欲罢不能。

*   **规则6.2：“三章一小节，十章一大节”原则**
    *   这是一个经典的网文节奏模型。大约每三章就要解决一个小冲突，给出一个小爽点（比如，打脸一个路人甲、学会一个新技能）。
    *   大约每十章就要完成一个阶段性事件，达成一个小目标，并引出更大的悬念。
    *   **AI指令示例**：“我需要写三章内容，完成一个‘主角被挑衅-被迫比试-最终获胜并获得奖励’的小情节弧光。请帮我规划一下每一章的核心内容。”

*   **规则6.3：爽点多样化原则**
    *   避免单一的“扮猪吃虎”或“打脸”套路。爽点可以有很多种形式：
        *   **实力提升爽**：突破境界、获得神兵利器。
        *   **知识信息爽**：获得他人不知道的秘密、预知未来。
        *   **人际关系爽**：获得美女青睐、收服强大手下、得到长辈赏识。
        *   **财富积累爽**：获得天价财富、捡漏稀有宝物。
    *   在创作时，有意识地交替使用不同类型的爽点，保持新鲜感。

#### **第七部分：对话与文风的“去AI味”规则**

AI生成的内容有时会显得刻板、书面化，需要人工优化。

*   **规则7.1：“角色声线”植入原则**
    *   为你的主要角色设定独特的“口头禅”或说话方式。例如，一个活泼的角色可能常用语气词，一个沉稳的角色则言简意赅。
    *   在AI生成对话后，手动修改，让对话更符合角色的性格设定，而不是千人一面。
    *   **AI指令示例**：“请写一段主角和他的话痨朋友的对话。主角性格沉稳，话少。朋友性格外向，喜欢开玩笑。对话内容是讨论即将到来的宗门大比。”

*   **规则7.2：黄金配角与“捧哏逗哏”原则**
    *   给主角安排一个功能性明确的配角，例如一个负责吐槽的“话痨”朋友，或是一个负责解说的“老爷爷”角色。
    *   通过他们与主角的互动，可以自然地引出设定、推动剧情，同时让对话变得生动有趣，避免主角自言自语。

#### **第八部分：数据驱动与读者互动的“运营”规则**

番茄小说是算法推荐平台，数据反馈至关重要。

*   **规则8.1：“章末钩子”最大化原则**
    *   每一章的结尾，都要刻意留下一个强有力的悬念（Cliffhanger）。这直接关系到读者的“追读率”，是平台判断作品潜力的核心数据。
    *   **钩子类型**：主角陷入绝境、新的敌人登场、一个惊天秘密即将揭晓、一个重要的选择摆在面前。
    *   **AI指令示例**：“请为这一章写一个结尾，要求在主角即将打开宝箱时，一个强大的敌人突然出现，并以‘你，也配动我的东西？’这句话结束。”

*   **规则8.2：评论区“埋梗”原则**
    *   在章节内容或“作者的话”中，有意识地设置一些槽点或问题，引导读者在评论区讨论。例如：“大家猜猜，这个神秘人到底是谁？”
    *   高活跃度的评论区有助于提升作品的推荐权重。

#### **第九部分：情感操纵与读者心锚规则**

爽点让读者“看下去”，而情感共鸣则让读者“爱上你”。

*   **规则9.1：“情绪过山车”与“压抑-释放”原则**
    *   **核心**：单纯的“一路爽”会使读者麻木。最顶级的爽感来自于“先抑后扬”。在给予巨大爽点之前，必须有足够的情绪铺垫和压抑。主角被鄙视得越惨、被压迫得越狠、面临的绝境越无解，当他最终翻盘打脸时，读者积蓄的情绪才能得到最彻底的释放，获得加倍的快感。
    *   **操作**：在一个大情节中，可以设计一个让主角暂时吃瘪、甚至被羞辱的桥段。关键在于，这个“瘪”不能是主角自身愚蠢造成的，而应是敌人过于强大、或是有客观限制。并在此期间，埋下翻盘的伏笔。
    *   **AI指令示例**：“请设计一个情节：主角为了保护家人，被迫向他的死对头下跪。要求详细描写主角内心的屈辱、周围人的嘲笑以及家人的担忧。同时，在情节中暗示主角正在 secretly 准备一个三天后能彻底毁灭这个死对头的计划。”

*   **规则9.2：“价值守护”与核心动机锚定原则**
    *   **核心**：主角的行为必须有一个读者能够理解并认同的“核心动机”。这个动机通常是守护某种珍视的价值，如亲情、爱情、尊严、家国等。当反派触及这个“逆鳞”时，主角的一切反击行为，无论多么残酷，都会被读者视为正义。这比单纯的“为宝物杀人”要高级得多。
    *   **操作**：在小说早期，就用具体情节建立起主角的“守护之物”。例如，花一章的篇幅去描写他和妹妹之间深厚的感情。当后期反派以妹妹为要挟时，读者的情绪就会被瞬间点燃。
    *   **AI指令示例**：“请写一个日常情节，内容是主角用他辛苦赚来的第一桶金，为他相依为命的妹妹买了一件她渴望已久的礼物。重点突出兄妹间的温馨互动和妹妹的喜悦，为后续的‘守护’情节埋下情感基础。”

#### **第十部分：商业化思维与算法博弈规则**

在番茄平台，你不仅是作者，也是产品经理。你的产品就是你的小说，你的用户就是读者，而算法就是渠道。

*   **规则10.1：“钩子前置”与“标题党”的艺术**
    *   **核心**：番茄的推荐机制非常看重“点击率”。一个引人注目的标题和简介，其重要性不亚于正文质量。标题要包含核心元素（如神医、系统、女帝），并带有悬念或冲突感。
    *   **操作**：在更新章节前，花时间思考三到五个备选标题，选择最有吸引力的一个。例如，《第10章：击败王强》远不如《第10章：一招秒杀！王家的脸都被打肿了！》。
    *   **AI指令示例**：“我本章的核心内容是‘主角在拍卖会上用低价买到了一个看似是废品，实则是神器的东西’。请帮我生成5个具有番茄小说风格的、吸引人的章节标题。”

*   **规则10.2：“广告卡点”与阅读时长最大化原则**
    *   **核心**：番茄是免费阅读+广告变现模式，读者的“有效阅读时长”是决定你收入的关键。这意味着，你需要让读者在广告出现时，依然有强烈的欲望继续读下去。
    *   **操作**：了解广告大致出现的章节位置（通常是章节末尾或中间）。在这些关键节点前，必须设置一个强力的“章中钩”或“章末钩”，让读者为了知道结果，心甘情愿地看完广告。这比单纯的章末悬念要求更高。
    *   **AI指令示例**：“请将这段‘主角与敌人对峙’的情节改写。要求在情节进行到最高潮，主角即将揭晓底牌的瞬间结束，制造一个强力的‘卡点’，让读者不看到下一段就浑身难受。”

#### **第十一部分：世界观延伸与“IP化”布局规则**

有远见的作者，在写第一章时，就已经在思考这本书的续集和衍生了。

*   **规则11.1：“留白”与“可扩展性”设定原则**
    *   **核心**：不要把世界观写得太“死”。在设定力量体系、地图或历史时，刻意留下一些“未解之谜”或“传说之地”。例如，“传说在九天之上，还有更高级的世界”、“据说开国皇帝的佩剑里，隐藏着一个天大的秘密”。
    *   **操作**：这些“留白”在前期是吸引读者的悬念，在后期则是你扩展剧情、开启新地图、甚至创作续集的“钩子”。当一个地图的剧情写完时，可以自然地通过这些线索过渡到下一个更大的舞台。
    *   **AI指令示例**：“在我的修仙世界观里，最高境界是‘渡劫期’。请帮我设计一些关于‘渡劫之后’的传说和谜团，要求这些传说听起来很模糊，但又充满诱惑，可以作为后期剧情的引子。”

*   **规则11.2：符号化与记忆点创造原则**
    *   **核心**：一个成功的IP，往往有几个标志性的符号。这可能是一句口头禅、一个独特的功法名称、一件标志性的武器、或是一个反复出现的配角。这些符号是读者讨论和记忆你的作品的核心。
    *   **操作**：有意识地为你的主角或世界观设计一些独特的“记忆点”，并在文中反复强化。
    *   **AI指令示例**：“请为我的主角设计一个独有的、名字听起来很霸气的招式，这个招式最好能体现他的‘火焰’属性和‘果断’的性格。同时，为这个招式设计一句简短而有力的出招口号。”